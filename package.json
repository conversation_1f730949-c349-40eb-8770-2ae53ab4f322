{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "3.1.1", "private": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "main": "prebuild_electron/main.js", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "electron:servewin": "pnpm prebuild:dev && concurrently -k \"pnpm dev\" \"pnpm electron\"", "electron:servemac": "pnpm prebuild:dev && concurrently -k \"pnpm dev\" \"pnpm electron:mac\" ", "electron:servelinux": "pnpm prebuild:dev && concurrently -k \"pnpm dev\" \"pnpm electron:linux\"", "electron:buildwin": "pnpm prebuild:win && vite build && electron-builder", "electron:buildwinarm": "pnpm prebuild:win && vite build && electron-builder --win --arm64", "electron:buildmac": "pnpm prebuild:mac && vite build && electron-builder --config.npmRebuild=false -c.extraMetadata.main=prebuild_electron/mac/mainMac.js", "electron": "wait-on tcp:3000 && cross-env NODE_ENV=development electron .", "electron:mac": "wait-on tcp:3000 && cross-env NODE_ENV=development electron prebuild_electron/mac/mainMac.js", "electron:linux": "wait-on tcp:3000 && cross-env NODE_ENV=development electron prebuild_electron/mac/mainMac.js --no-sandbox", "prebuild:win": "rolldown -c rolldown-win.config.js", "prebuild:mac": "rolldown -c rolldown-mac.config.js", "prebuild:dev": "rolldown -c", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@electron/remote": "^2.1.2", "electron-store": "^8.1.0", "mica-electron": "^1.5.16"}, "devDependencies": {"@antfu/eslint-config": "^3.12.0", "@iconify-json/circle-flags": "^1.2.1", "@iconify-json/emojione-v1": "^1.2.0", "@iconify-json/f7": "^1.2.1", "@iconify-json/flagpack": "^1.2.1", "@iconify-json/fluent": "^1.2.7", "@iconify-json/icon-park-outline": "^1.2.1", "@iconify-json/logos": "^1.2.3", "@iconify-json/mdi": "^1.2.1", "@iconify-json/ph": "^1.2.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.1", "@stylistic/eslint-plugin": "^2.12.1", "@tomjs/electron-devtools-installer": "^2.4.0", "@types/node": "^20.14.0", "@unocss/eslint-plugin": "^0.65.1", "@unocss/preset-icons": "^0.65.1", "@unocss/transformer-attributify-jsx": "^0.65.1", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue-vine/eslint-config": "^0.2.11", "@vueuse/components": "^12.0.0", "@vueuse/core": "^12.0.0", "concurrently": "^9.1.0", "cross-env": "^7.0.3", "electron": "^33.2.1", "electron-builder": "^25.1.8", "element-plus": "^2.9.0", "es-toolkit": "^1.30.1", "eslint": "^9.17.0", "eslint-plugin-format": "^0.1.3", "floating-vue": "5.2.2", "mitt": "^3.0.1", "moment": "^2.30.1", "rolldown": "0.15.0", "rollup-plugin-clear": "^2.0.7", "rollup-plugin-dts": "^6.1.1", "sass": "^1.82.0", "tslib": "^2.8.1", "typescript": "^5.7.2", "unocss": "^0.65.1", "unplugin-element-plus": "^0.8.0", "unplugin-vue-macros": "^2.13.6", "vite": "^6.0.3", "vite-plugin-electron-renderer": "^0.14.6", "vue": "^3.5.13", "vue-i18n": "10.0.5", "vue-router": "^4.5.0", "vue-tsc": "^2.1.10", "vue-vine": "^0.1.41", "vue-vine-tsc": "^0.0.12", "vue3-perfect-scrollbar": "^2.0.0", "wait-on": "^8.0.1"}, "volta": {"node": "20.14.0", "pnpm": "9.0.6"}}