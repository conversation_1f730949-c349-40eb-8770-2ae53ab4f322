::-webkit-scrollbar {
  --uno: hidden;
}

.black-back {
  --uno: bg-black/45 backdrop-blur h-screen w-[calc(100vw-300px)] fixed top-50% right-0 translate-x-0 translate-y--50% mt-25px;
}

body {
  --uno: m-0 p-0;

  #app {
    --uno: select-none;
  }
}

input[disabled] {
  --uno: cursor-not-allowed;
}

.v-popper__popper {
  --uno: no-drag;

  .v-popper__wrapper {
    .v-popper__inner {
      --uno: bg-white/90 rounded-7px backdrop-blur c-#555;
    }

    .v-popper__arrow-inner,
    .v-popper__arrow-outer {
      --uno: border-white/90;
    }
  }

  &.v-popper__popper--no-positioning {
    --uno: fixed z-9999 top-0 l-0 w-100% h-100% flex items-end;

    .v-popper__backdrop {
      --uno: block bg-black/20;
    }

    .v-popper__wrapper {
      --uno: w-100% pointer-events-auto transition-transform transition-150 transition-ease-out m-10px mb-50px;
    }

    &.v-popper__popper--hidden {
      .v-popper__wrapper {
        --uno: translate-y-100%;
      }
    }
  }
}

@media (prefers-color-scheme: dark) {
  .v-popper__popper {
    .v-popper__wrapper {
      .v-popper__inner {
        --uno: bg-#444/90 c-#bbb border-#444/90;

        button {
          --uno: bg-#555 c-#bbb active: bg-#505050;
        }
      }

      .v-popper__arrow-inner,
      .v-popper__arrow-outer {
        --uno: border-#444/90;
      }
    }
  }
}

.el-picker__popper {
  --uno: no-drag;
}

.ps {
  padding-inline-start: 0 !important;
}
