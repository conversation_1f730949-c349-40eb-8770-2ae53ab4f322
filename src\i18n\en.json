{"addText": "Add", "cancelText": "Cancel", "copyText": "Copy", "copyToast": "Copy successfully", "settingTitleText": "Settings", "setLangText": "Auto", "updateText": "New Version", "loginText": "Not logged in", "myAccount": "My Account", "setTopState": "Save Top Status", "saveWindowSize": "Save Window Size", "clearData": "Clear Data", "useSystemBar": "Use The System Title Bar", "setTopWindow": "Top Window", "restartApp": "Restart the app to take effect", "closeWindow": "Do you want to close uyou ToDo?", "accountPage": {"account": "Account", "passwd": "Password", "login": "Log in", "register": "Register", "autoSync": "Automatic sync", "logout": "Log out", "alertNoAnP": "Please enter account and password", "syncData": "Synchronous Data", "syncSuccess": "Sync succeeded", "syncFail": "Sync failed", "loginError": "<PERSON><PERSON> failed", "alertTitle": "Hint", "changPass": "change Password", "logoutMsg": "Are you sure you want to log out?", "userNum": "Registered user"}, "listMenu": {"cate": "Categories", "allTodo": "All ToDos", "completed": "Completed", "incompleted": "Incompleted", "star": "Starred <PERSON>", "to": "Move to ", "delCate": "Are you sure you want to delete {title}?", "delTodo": "Delete ToDos together"}, "alertText": {"cancelText": "Cancel", "returnText": "Confirm"}, "firstLoadData": ["Welcome to use uyou ToDo", "Move the mouse to the left of the ToDo item to complete the ToDo", "Move the mouse to the right of the ToDo item to delete the ToDo", "Move the mouse to the top right corner of the ToDo title bar to copy the ToDo content", "Star ToDo", "You can set multiple ToDo categories"], "update": {"autoUpdate": "Get Updates Automatically", "updateTitle": "App Update", "notUpdate": "No update yet", "checkingUpdate": "Checking for updates...", "checkUpdate": "Check for updates", "gotoUpdate": "Go to update", "updateLog": "Changelog:"}, "otherList": {"toWeb": "Go to the official website", "toPhone": "Mobile version", "toDonate": "Buy a cup of coffee for me", "toGithub": "Go to GitHub", "toFind": "Contact Me", "aboutSponser": "About Sponsor"}, "anotherSettings": {"menuBlur": "Blurred window", "windowMenu": "Window Menu", "openSource": "Thanks Open Source", "about": "About uyou ToDo", "simple": "Simple Mode", "enterToAdd": "Enter to add ToDo", "autoStart": "Open With System", "itemWrap": "ToDo default open", "laboratory": "Laboratory", "itemBtnShow": "Show ToDo button", "rememberQuit": "Remember to close the window", "openPass": "Start encryption", "model": "Mode selection", "star": "show star button", "ok": "Show Completed <PERSON><PERSON>", "backup": "Local backup <PERSON><PERSON><PERSON>"}, "contextMenu": {"copy": "Copy", "paste": "Paste", "comToDo": "Complete ToDo", "removeToDo": "Delete ToDo", "clearTxt": "Clear Text", "closeItem": "Close Item", "undoTodo": "Undo Completed", "unstarred": "Unstar", "star": "Star", "edit": "Edit <PERSON>"}, "language": "Language", "registerPage": {"account": "Account: ", "password": "Password: ", "rePass": "Repeat password: ", "reg": "Register", "plzAccAndPass": "Please enter account and password", "onlyNum": "Only letters, numbers, and underscores are supported", "accLen": "Account length cannot exceed 10 characters", "rePassError": "Repeat password does not match secret", "regSuccess": "Registration success!", "regFail": "Registration failed!"}, "rePassPage": {"oldPass": "Old Password: ", "newPass": "New Password: ", "setPass": "Reset Password", "plzInOldPass": "Please enter old password", "plzInNewPass": "Please enter a new password", "setPassSuccess": "Success to change password!", "setPassFail": "Failed to change password!"}, "logoffPage": {"logoff": "Log Off", "success": "Log off success", "fail": "Log off fail", "passNotTrue": "password is not true"}, "startPage": {"today": "My Today", "allTodos": "All ToDos", "startPage": "Start Page"}, "timeAgo": {"just-now": "just now", "ago": "{0} ago", "in": "in {0}", "last-month": "last month", "next-month": "next month", "month": "month | months", "last-year": "last year", "next-year": "next year", "year": "year | years", "yesterday": "yesterday", "tomorrow": "tomorrow", "day": "day | days", "last-week": "last week", "next-week": "next week", "week": "week | weeks", "hour": "hour | hours", "minute": "minute | minutes", "second": "second | seconds"}, "add-category": "Add Category", "today": "Today ", "pick-a-day": "Choose reminder time", "todo-time": "<PERSON><PERSON><PERSON>", "quit": {"tray": "Minimize to system tray", "quit": "Quit", "remember": "remember next time", "closeMsgBox": "Don't show close dialog"}, "custListItem": "Custom Sidebar Categories", "custList": {"showAll": "show all", "today": "My Today", "star": "Starred <PERSON>", "completed": "Completed", "incompleted": "Incompleted"}, "vip": {"proVersion": "uyou ToDo pro version", "8yuan": "0r ￥8.00 + GitHub Star", "msg": "If you like uyou ToDo, you can try to pay me\nThe payment mode is honest payment, inspired by salt and pepper music\nOf course, you can also use it yourself without paying. Of course, using it after paying is a sign of integrity\nPlease consider before paying", "isPay": "I have paid in good faith and activated the professional version", "more": "More features, please stay tuned...", "proFeature": "Pro features", "showList": "Custom display categories", "custColor": "Custom category colors", "doPay": "Has paid in good faith uyou ToDo Professional Edition", "thanks": "Thank you for your support, this will help uyou ToDo develop better", "floatUI": "Use Float UI", "setCustFont": "Set custom font", "setCustPassKey": "Custom encryption keyboard style"}, "openPass": {"useOpenPass": "Use startup encryption", "usePass": "Use password", "clearPass": "Clear password", "plzPass": "Please enter password", "passErr": "Wrong password, please re-enter", "longPress": "Long press to delete"}, "todayShow": {"creatTime": "Show only those created today", "remindTime": "Only show reminders for today", "allTime": "Show all about today"}, "setFont": {"useCustomFont": "Use custom font", "donnotSelect": "No font selected", "select": "chosen", "regular": "Regular", "bold": "Bold", "setFontSize": "Set font size", "setFont": "Set font", "small": "small", "normal": "standard", "big": "big", "large": "large"}, "setPassKey": {"useCustPass": "Use a custom lock keyboard"}, "noteui": {"noData": "no data", "other": "uncategorized", "addCate": "Add Category", "cancelAdd": "Cancel To Add", "chooseIcon": "Select category icon", "cateName": "Category name", "chooseColor": "Select category color", "addItem": "Confirm To Add", "allcate": "All Categories", "othercate": "Other categories", "search": "Search", "spcate": "Special Category", "closetext": "Collapse text", "opentext": "Expand text"}, "sponsor": {"thanks": "Thanks Sponsors"}, "mode": {"normal": "Standard Mode", "note": "Note Mode"}, "newCate": "new category", "setup": {"touse": "Go to use", "toset": "Go to \"Settings\" -> \"Mode Selection\" to reselect the mode"}, "note": {"select": "Select category"}, "open": {"thanks": "The development of uyou ToDo is inseparable from the open source community\n\nThanks to the following open source projects to build uyou ToDo"}, "seccate": "Category 2", "afd": "Click to go", "delAllDo": "Delete all completed ToDos?", "backup": "Synchronize ToDo content and categories on different devices by backing up to local files without logging in", "backupT": {"export": "Export backup", "import": "Import backup", "exportCate": "Export categories", "importToDo": "Import ToDo", "importCate": "Import categories", "warn": "Note that the data will be overwritten after importing and cannot be recovered! \n! \n!", "todo": "ToDo data", "cate": "Category data", "importSuccess": "Import successful", "exportSuccess": "Export successful", "exportToDo": "Export ToDo", "warn2": "Be sure to export or import both ToDo data and category data! \n! \n!"}, "win95Btn": {"Toast": "Win95 good!", "Button": "Go to uyou ToDo 95", "closeToast": "Goodbye Win95!"}}